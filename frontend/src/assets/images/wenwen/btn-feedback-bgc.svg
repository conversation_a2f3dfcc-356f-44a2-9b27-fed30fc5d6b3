<svg width="107" height="52" viewBox="0 0 107 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2586_430)">
<path d="M75 4L96.855 11.1049C99.3267 11.9084 101 14.2119 101 16.8109V35.1891C101 37.7881 99.3267 40.0916 96.855 40.8951L75 48V4Z" fill="#CF1A1C" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<rect y="4" width="75" height="44" fill="#CF1A1C" fill-opacity="0.8"/>
<defs>
<filter id="filter0_d_2586_430" x="73" y="0" width="34" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2586_430"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2586_430" result="shape"/>
</filter>
</defs>
</svg>
